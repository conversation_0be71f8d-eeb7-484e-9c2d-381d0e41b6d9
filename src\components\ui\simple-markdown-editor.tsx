import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import Highlight from '@tiptap/extension-highlight';
import Strike from '@tiptap/extension-strike';
import Underline from '@tiptap/extension-underline';
import Placeholder from '@tiptap/extension-placeholder';
import Youtube from '@tiptap/extension-youtube';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { MarkdownPreview } from '@/components/ui/markdown-preview';
import { tiptapToMarkdown, htmlToMarkdown } from '@/lib/enhanced-markdown-serializer';
import { markdownToHtml } from '@/lib/content-converter';
import { uploadEditorImage } from '@/lib/tiptap-image-upload';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import {
  Bold,
  Italic,
  List,
  ListOrdered,
  Code,
  Link as LinkIcon,
  Image as ImageIcon,
  Table as TableIcon,
  Type,
  Eye,
  Upload,
  Loader2,
  Youtube as YoutubeIcon,
} from 'lucide-react';

interface SimpleMarkdownEditorProps {
  initialContent?: string;
  onChange?: (markdown: string) => void;
  placeholder?: string;
  className?: string;
  minHeight?: number;
  courseId?: string;
  moduleId?: string;
}

export function SimpleMarkdownEditor({
  initialContent = '',
  onChange,
  placeholder = 'Start writing...',
  className = '',
  minHeight = 300,
  courseId,
  moduleId,
}: SimpleMarkdownEditorProps) {
  const [activeTab, setActiveTab] = useState<'editor' | 'preview'>('editor');
  const [markdownContent, setMarkdownContent] = useState(initialContent);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [isYoutubeDialogOpen, setIsYoutubeDialogOpen] = useState(false);
  const [youtubeUrl, setYoutubeUrl] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const editor = useEditor({
    extensions: [
      StarterKit,
      Image.configure({
        HTMLAttributes: {
          class: 'rounded max-w-full h-auto',
        },
        inline: false,
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline',
        },
      }),
      Table.configure({
        resizable: true,
        HTMLAttributes: {
          class: 'border-collapse border border-gray-300',
        },
      }),
      TableRow,
      TableHeader.configure({
        HTMLAttributes: {
          class: 'bg-gray-100 font-semibold',
        },
      }),
      TableCell.configure({
        HTMLAttributes: {
          class: 'border border-gray-300 px-2 py-1',
        },
      }),
      TaskList.configure({
        HTMLAttributes: {
          class: 'task-list',
        },
      }),
      TaskItem.configure({
        HTMLAttributes: {
          class: 'task-item flex items-start gap-2',
        },
        nested: true,
      }),
      Highlight.configure({
        HTMLAttributes: {
          class: 'bg-yellow-200 dark:bg-yellow-800 px-1 rounded',
        },
      }),
      Strike.configure({
        HTMLAttributes: {
          class: 'line-through',
        },
      }),
      Underline,
      Youtube.configure({
        controls: true,
        nocookie: true,
        HTMLAttributes: {
          class: 'rounded-lg overflow-hidden my-4',
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
    ],
    content: markdownToHtml(initialContent),
    onUpdate: ({ editor }) => {
      try {
        const doc = editor.state.doc;
        const markdown = tiptapToMarkdown(doc);
        setMarkdownContent(markdown);
        onChange?.(markdown);
      } catch (error) {
        const html = editor.getHTML();
        const markdown = htmlToMarkdown(html);
        setMarkdownContent(markdown);
        onChange?.(markdown);
      }
    },
    editorProps: {
      attributes: {
        class: cn(
          'prose max-w-none focus:outline-none p-4',
          `min-h-[${minHeight}px]`
        ),
      },
    },
  });

  useEffect(() => {
    if (editor && initialContent !== markdownContent) {
      const htmlContent = markdownToHtml(initialContent);
      editor.commands.setContent(htmlContent);
      setMarkdownContent(initialContent);
    }
  }, [initialContent, editor, markdownContent]);

  // Enhanced image insertion with file upload support
  const addImage = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, []);

  const addImageFromUrl = useCallback(() => {
    const url = prompt('Enter image URL:');
    if (url && editor) {
      editor.chain().focus().setImage({ src: url }).run();
      toast.success('Image added!');
    }
  }, [editor]);

  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !editor) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('Image size must be less than 10MB');
      return;
    }

    setIsUploadingImage(true);

    try {
      const imageUrl = await uploadEditorImage(file, courseId, moduleId);
      editor.chain().focus().setImage({ src: imageUrl }).run();
      toast.success('Image uploaded and added!');
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image. Please try again.');
    } finally {
      setIsUploadingImage(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }, [editor, courseId, moduleId]);

  const addLink = useCallback(() => {
    const url = prompt('Enter URL:');
    if (url && editor) {
      editor.chain().focus().setLink({ href: url }).run();
    }
  }, [editor]);

  const addTable = useCallback(() => {
    if (editor) {
      editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
    }
  }, [editor]);

  const addYoutubeVideo = useCallback(() => {
    setIsYoutubeDialogOpen(true);
  }, []);

  if (!editor) return null;

  const ToolButton = ({ onClick, isActive = false, children, title }: any) => (
    <Button
      type="button"
      variant="ghost"
      size="sm"
      onClick={onClick}
      className={cn(
        "h-8 w-8 p-0",
        isActive ? 'bg-muted' : ''
      )}
      title={title}
    >
      {children}
    </Button>
  );

  return (
    <div className={cn("border rounded-lg bg-card", className)}>
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'editor' | 'preview')}>
        {/* Simple Tab Header */}
        <div className="flex items-center justify-between border-b p-2">
          <TabsList className="bg-transparent">
            <TabsTrigger value="editor" className="flex items-center gap-1">
              <Type className="h-3 w-3" />
              Write
            </TabsTrigger>
            <TabsTrigger value="preview" className="flex items-center gap-1">
              <Eye className="h-3 w-3" />
              Preview
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="editor" className="p-0 m-0">
          {/* Simple Toolbar */}
          <div className="flex items-center gap-1 p-2 border-b bg-muted/30">
            <ToolButton
              onClick={() => editor.chain().focus().toggleBold().run()}
              isActive={editor.isActive('bold')}
              title="Bold"
            >
              <Bold className="h-4 w-4" />
            </ToolButton>
            
            <ToolButton
              onClick={() => editor.chain().focus().toggleItalic().run()}
              isActive={editor.isActive('italic')}
              title="Italic"
            >
              <Italic className="h-4 w-4" />
            </ToolButton>
            
            <ToolButton
              onClick={() => editor.chain().focus().toggleCode().run()}
              isActive={editor.isActive('code')}
              title="Code"
            >
              <Code className="h-4 w-4" />
            </ToolButton>

            <div className="w-px h-6 bg-gray-300 mx-1" />

            <ToolButton
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              isActive={editor.isActive('bulletList')}
              title="List"
            >
              <List className="h-4 w-4" />
            </ToolButton>
            
            <ToolButton
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              isActive={editor.isActive('orderedList')}
              title="Numbered List"
            >
              <ListOrdered className="h-4 w-4" />
            </ToolButton>

            <div className="w-px h-6 bg-gray-300 mx-1" />

            <ToolButton onClick={addLink} title="Add Link">
              <LinkIcon className="h-4 w-4" />
            </ToolButton>

            <ToolButton
              onClick={addImage}
              title="Upload Image"
              disabled={isUploadingImage}
            >
              {isUploadingImage ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Upload className="h-4 w-4" />
              )}
            </ToolButton>

            <ToolButton onClick={addImageFromUrl} title="Add Image from URL">
              <ImageIcon className="h-4 w-4" />
            </ToolButton>

            <ToolButton onClick={addTable} title="Add Table">
              <TableIcon className="h-4 w-4" />
            </ToolButton>

            <ToolButton onClick={addYoutubeVideo} title="Add YouTube Video">
              <YoutubeIcon className="h-4 w-4" />
            </ToolButton>
          </div>

          <EditorContent 
            editor={editor} 
            style={{ minHeight: `${minHeight}px` }}
          />
        </TabsContent>

        <TabsContent value="preview" className="p-4 m-0" style={{ minHeight: `${minHeight}px` }}>
          <div className="github-markdown-preview">
            <MarkdownPreview
              content={markdownContent}
              className="prose max-w-none"
              allowHtml={true}
            />
          </div>
        </TabsContent>
      </Tabs>

      {/* Hidden file input for image uploads */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileUpload}
        style={{ display: 'none' }}
      />

      {/* YouTube Dialog */}
      <Dialog open={isYoutubeDialogOpen} onOpenChange={setIsYoutubeDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Insert YouTube Video</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="youtube-url">YouTube URL</Label>
              <Input
                id="youtube-url"
                placeholder="https://www.youtube.com/watch?v=..."
                value={youtubeUrl}
                onChange={(e) => setYoutubeUrl(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsYoutubeDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (youtubeUrl) {
                  editor?.chain().focus().setYoutubeVideo({ src: youtubeUrl }).run();
                  toast.success('YouTube video added!');
                }
                setIsYoutubeDialogOpen(false);
                setYoutubeUrl('');
              }}
              disabled={!youtubeUrl}
            >
              Insert Video
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
