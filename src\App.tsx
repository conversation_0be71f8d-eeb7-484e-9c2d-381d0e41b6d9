import React, { lazy, Suspense, useEffect } from 'react';
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";
import { EnhancedThemeProvider } from "./components/theme/enhanced-theme-provider";
import { MotionProvider } from "./context/MotionContext";
import { OnboardingWrapper } from "./components/onboarding/OnboardingWrapper";
import OfflineIndicator from "./components/OfflineIndicator";
import OfflineQueueStatus from "./components/OfflineQueueStatus";
import ConnectionStatusIndicator from "./components/ConnectionStatusIndicator";
import SupabaseErrorBoundary from '@/components/error-boundaries/SupabaseErrorBoundary';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import RoleProtectedRoute from '@/components/auth/RoleProtectedRoute';
import ForceRedTheme from './components/theme/force-red-theme';
import Footer from './components/layouts/Footer';
import * as Sentry from '@sentry/react';
import { BrowserTracing } from '@sentry/tracing';

// Import the Index page directly since it's the landing page
import Index from "./pages/Index";

// Lazy load all other pages for better performance
const Login = lazy(() => import("./pages/Login"));
const ResetPassword = lazy(() => import("./pages/ResetPassword"));
const Dashboard = lazy(() => import("./pages/Dashboard"));
const ModuleContent = lazy(() => import("./pages/ModuleContent"));
const ModuleManagementPage = lazy(() => import("./pages/ModuleManagementPage"));
const ModulesPage = lazy(() => import("./pages/ModulesPage"));
const ModuleTestPage = lazy(() => import("./pages/ModuleTestPage"));
const LessonContent = lazy(() => import("./pages/LessonContent"));
const NotFound = lazy(() => import("./pages/NotFound"));
const Admin = lazy(() => import("./pages/Admin"));
const CertificatePage = lazy(() => import("./pages/CertificatePage"));
const EditorDemo = lazy(() => import("./pages/EditorDemo"));
const MarkdownEditorDemo = lazy(() => import("./pages/MarkdownEditorDemo"));
const MarkdownDebug = lazy(() => import("./components/debug/MarkdownDebug"));
const TableTest = lazy(() => import("./components/debug/TableTest"));
const MarkdownEditorTest = lazy(() => import("./pages/MarkdownEditorTest"));
const TableRenderingTest = lazy(() => import("./pages/TableRenderingTest"));
const MarkdownTableTest = lazy(() => import("./pages/MarkdownTableTest"));
const TableParsingVerification = lazy(() => import("./pages/TableParsingVerification"));
const AchievementsPage = lazy(() => import("./pages/AchievementsPage"));
const TestLessonUI = lazy(() => import("./pages/TestLessonUI"));
const TestMarkdownImages = lazy(() => import("./pages/TestMarkdownImages"));
const SpacingTestPage = lazy(() => import("./pages/SpacingTestPage"));
const DebugLesson = lazy(() => import("./pages/DebugLesson"));
const ImageDebugTest = lazy(() => import("./components/debug/ImageDebugTest"));
const ImageTestPage = lazy(() => import("./pages/ImageTestPage"));
const LessonRedirect = lazy(() => import("./components/LessonRedirect"));
const YouTubeEditorTest = lazy(() => import("./pages/YouTubeEditorTest"));
const NavigationTest = lazy(() => import("./pages/NavigationTest"));
// Removed image upload test pages

const AuthCallback = lazy(() => import("./pages/AuthCallback"));

// Admin pages
// const AdminDashboard = lazy(() => import('./pages/admin/AdminDashboard'));
// const AdminUsers = lazy(() => import('./pages/admin/AdminUsers'));
// const AdminCourses = lazy(() => import('./pages/admin/AdminCourses'));
const CompletionDiagnostics = lazy(() => import('./pages/admin/CompletionDiagnostics'));
const TestAnalyticsPage = lazy(() => import('./pages/admin/TestAnalyticsPage'));
// const SettingsPage = lazy(() => import('./pages/SettingsPage'));

// Import standardized loading component
import { PageLoadingSpinner } from '@/components/ui/loading-spinner';

// Preload critical routes after initial render
const preloadRoutes = () => {
  // Use requestIdleCallback for better performance
  if ('requestIdleCallback' in window) {
    window.requestIdleCallback(() => {
      // Only preload the most critical routes
      const currentPath = window.location.pathname;

      // Preload based on current path
      if (currentPath === '/') {
        import("./pages/Login");
      } else if (currentPath.includes('/dashboard')) {
        import("./pages/Dashboard");
      } else if (currentPath.includes('/course')) {
        import("./pages/ModuleContent");
      }
    }, { timeout: 5000 }); // Increased timeout to reduce priority
  } else {
    // Fallback for browsers that don't support requestIdleCallback
    setTimeout(() => {
      const currentPath = window.location.pathname;

      // Only preload the route that matches the current path
      if (currentPath === '/') {
        import("./pages/Login");
      } else if (currentPath.includes('/dashboard')) {
        import("./pages/Dashboard");
      } else if (currentPath.includes('/course')) {
        import("./pages/ModuleContent");
      }
    }, 3000); // Increased delay to 3 seconds
  }
};

// Create a new QueryClient instance with optimized settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false, // Disable refetching when window regains focus
      retry: false, // Don't retry failed queries automatically
      staleTime: 10 * 60 * 1000, // Consider data fresh for 10 minutes
      refetchOnMount: false, // Don't refetch data when component mounts
      retryDelay: 1000, // If retry is enabled elsewhere, use a fixed delay
    },
    mutations: {
      retry: false, // Don't retry failed mutations
    },
  },
});

Sentry.init({
  dsn: 'YOUR_SENTRY_DSN', // TODO: Replace with your Sentry DSN
  integrations: [new BrowserTracing()],
  tracesSampleRate: 0.2, // Adjust for your needs
  environment: import.meta.env.MODE,
});

const PLAUSIBLE_DOMAIN = 'yourdomain.com'; // TODO: Set your production domain

const App = () => {
  // Initialize preloading of critical routes
  useEffect(() => {
    // Add Plausible Analytics script
    if (window.location.hostname !== 'localhost') {
      const script = document.createElement('script');
      script.setAttribute('defer', '');
      script.setAttribute('data-domain', PLAUSIBLE_DOMAIN);
      script.src = 'https://plausible.io/js/plausible.js';
      document.body.appendChild(script);
    }
    // Immediately mark document as loaded to improve performance
    document.documentElement.classList.add('loaded');

    // Force remove the initial loader after a short timeout
    // This ensures the loader is removed even if there are issues with the app
    const forceRemoveLoader = setTimeout(() => {
      const loader = document.getElementById('initial-loader');
      if (loader && loader.parentNode) {
        loader.parentNode.removeChild(loader);
        console.log('Loader forcibly removed after timeout');
      }
    }, 2000); // Force remove after 2 seconds max

    // Normal loader removal (will happen sooner if everything loads properly)
    const loader = document.getElementById('initial-loader');
    if (loader) {
      // Fade out the loader
      loader.style.opacity = '0';

      // Remove the loader from DOM after fade out
      setTimeout(() => {
        if (loader.parentNode) {
          loader.parentNode.removeChild(loader);
          console.log('Loader removed normally');

          // Cancel the force remove since we've already removed it
          clearTimeout(forceRemoveLoader);
        }
      }, 300);
    }

    // Preload routes after a delay to prioritize UI rendering
    setTimeout(preloadRoutes, 1500);

    // Mark as fully loaded when the window load event fires
    const handleLoad = () => {
      document.documentElement.classList.add('fully-loaded');
      console.log('Document fully loaded');
    };

    window.addEventListener('load', handleLoad);

    return () => {
      clearTimeout(forceRemoveLoader);
      window.removeEventListener('load', handleLoad);
    };
  }, []);

  return (
    <Sentry.ErrorBoundary fallback={<div>Something went wrong. Please refresh the page.</div>} showDialog>
      <EnhancedThemeProvider defaultTheme="system">
        <ForceRedTheme />
        <MotionProvider>
          <QueryClientProvider client={queryClient}>
            <TooltipProvider>
              <Sonner />
              <OfflineIndicator />
              <OfflineQueueStatus />
              <ConnectionStatusIndicator />
              <BrowserRouter>
                <AuthProvider>
                  <OnboardingWrapper>
                    <Routes>
                      <Route path="/" element={<Index />} />
                      <Route path="/login" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading login..." />}>
                          <Login />
                        </Suspense>
                      } />

                      <Route path="/reset-password" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading..." />}>
                          <ResetPassword />
                        </Suspense>
                      } />
                      <Route path="/auth/callback" element={
                        <Suspense fallback={<PageLoadingSpinner text="Authenticating..." />}>
                          <AuthCallback />
                        </Suspense>
                      } />
                      <Route path="/dashboard" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading dashboard..." />}>
                          <SupabaseErrorBoundary>
                            <ProtectedRoute>
                              <Dashboard />
                            </ProtectedRoute>
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/my-courses" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading courses..." />}>
                          <SupabaseErrorBoundary>
                            <Dashboard />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/achievements" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading achievements..." />}>
                          <SupabaseErrorBoundary>
                            <AchievementsPage />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/course/:courseId" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading course..." />}>
                          <SupabaseErrorBoundary>
                            <ModuleContent />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/course/:courseId/modules" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading modules..." />}>
                          <SupabaseErrorBoundary>
                            <ModulesPage />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/course/:courseId/lesson/:lessonId" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading lesson..." />}>
                          <SupabaseErrorBoundary>
                            <LessonContent />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/lesson/:lessonSlug" element={
                        <Suspense fallback={<PageLoadingSpinner text="Redirecting to lesson..." />}>
                          <SupabaseErrorBoundary>
                            <LessonRedirect />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/course/:courseId/module/:moduleId" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading module test..." />}>
                          <SupabaseErrorBoundary>
                            <ModuleTestPage />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/certificate/:courseId" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading certificate..." />}>
                          <SupabaseErrorBoundary>
                            <CertificatePage />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/editor-demo" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading editor demo..." />}>
                          <SupabaseErrorBoundary>
                            <EditorDemo />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/markdown-editor-demo" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading markdown editor demo..." />}>
                          <SupabaseErrorBoundary>
                            <MarkdownEditorDemo />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/markdown-debug" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading markdown debug..." />}>
                          <SupabaseErrorBoundary>
                            <MarkdownDebug />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/table-test" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading table test..." />}>
                          <SupabaseErrorBoundary>
                            <TableTest />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/markdown-editor-test" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading markdown editor test..." />}>
                          <SupabaseErrorBoundary>
                            <MarkdownEditorTest />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/table-rendering-test" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading table rendering test..." />}>
                          <SupabaseErrorBoundary>
                            <TableRenderingTest />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/markdown-table-test" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading markdown table test..." />}>
                          <SupabaseErrorBoundary>
                            <MarkdownTableTest />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/table-parsing-verification" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading table parsing verification..." />}>
                          <SupabaseErrorBoundary>
                            <TableParsingVerification />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/test-lesson-ui" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading lesson UI test..." />}>
                          <SupabaseErrorBoundary>
                            <TestLessonUI />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/test-markdown-images" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading markdown image test..." />}>
                          <SupabaseErrorBoundary>
                            <TestMarkdownImages />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/spacing-test" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading spacing test..." />}>
                          <SupabaseErrorBoundary>
                            <SpacingTestPage />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/debug-lesson" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading debug lesson..." />}>
                          <SupabaseErrorBoundary>
                            <DebugLesson />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/navigation-test" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading navigation test..." />}>
                          <SupabaseErrorBoundary>
                            <NavigationTest />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/image-debug-test" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading image debug test..." />}>
                          <SupabaseErrorBoundary>
                            <ImageDebugTest />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/image-test" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading image test..." />}>
                          <SupabaseErrorBoundary>
                            <ImageTestPage />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/youtube-editor-test" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading YouTube editor test..." />}>
                          <SupabaseErrorBoundary>
                            <YouTubeEditorTest />
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      {/* Removed image upload test routes */}
                      <Route path="/admin" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading admin panel..." />}>
                          <SupabaseErrorBoundary>
                            <RoleProtectedRoute requiredRole="teacher">
                              <Admin />
                            </RoleProtectedRoute>
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/module-management/:courseId" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading module management..." />}>
                          <SupabaseErrorBoundary>
                            <RoleProtectedRoute requiredRole="teacher">
                              <ModuleManagementPage />
                            </RoleProtectedRoute>
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/admin/progress" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading diagnostics..." />}>
                          <SupabaseErrorBoundary>
                            <RoleProtectedRoute requiredRole="admin">
                              <CompletionDiagnostics />
                            </RoleProtectedRoute>
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      <Route path="/admin/analytics" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading test analytics..." />}>
                          <SupabaseErrorBoundary>
                            <RoleProtectedRoute requiredRole="teacher">
                              <TestAnalyticsPage />
                            </RoleProtectedRoute>
                          </SupabaseErrorBoundary>
                        </Suspense>
                      } />
                      {/* Comment out routes for non-existent admin pages */}
                      {/*
                      <Route path="/admin/dashboard" element={
                        <RoleProtectedRoute requiredRole="admin">
                          <Suspense fallback={<LoadingSpinner />}>
                            <AdminDashboard />
                          </Suspense>
                        </RoleProtectedRoute>
                      } />
                      <Route path="/admin/users" element={
                        <RoleProtectedRoute requiredRole="admin">
                          <Suspense fallback={<LoadingSpinner />}>
                            <AdminUsers />
                          </Suspense>
                        </RoleProtectedRoute>
                      } />
                      <Route path="/admin/courses" element={
                        <RoleProtectedRoute requiredRole="admin">
                          <Suspense fallback={<LoadingSpinner />}>
                            <AdminCourses />
                          </Suspense>
                        </RoleProtectedRoute>
                      } />
                      <Route path="/settings" element={
                        <ProtectedRoute>
                          <Suspense fallback={<LoadingSpinner />}>
                            <SettingsPage />
                          </Suspense>
                        </ProtectedRoute>
                      } />
                      */}
                      <Route path="*" element={
                        <Suspense fallback={<PageLoadingSpinner text="Loading..." />}>
                          <NotFound />
                        </Suspense>
                      } />
                    </Routes>
                  </OnboardingWrapper>
                </AuthProvider>
              </BrowserRouter>
              <Footer />
            </TooltipProvider>
          </QueryClientProvider>
        </MotionProvider>
      </EnhancedThemeProvider>
    </Sentry.ErrorBoundary>
  );
};

export default App;
