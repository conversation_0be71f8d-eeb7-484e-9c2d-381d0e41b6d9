
/**
 * Utility functions for handling file uploads
 */

/**
 * Convert a File object to a base64 data URL
 */
export const fileToDataUrl = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

/**
 * Get file type category (image, video, other)
 */
export const getFileType = (file: File): 'image' | 'video' | 'other' => {
  if (file.type.startsWith('image/')) return 'image';
  if (file.type.startsWith('video/')) return 'video';
  return 'other';
};

/**
 * Convert video file to embedded URL format if needed
 * For uploaded videos, we use the data URL directly
 */
export const processVideoUrl = (url: string): string => {
  if (!url || typeof url !== 'string') return '';

  // For data URLs, return as is
  if (url.startsWith('data:')) return url;

  // Handle YouTube URLs
  if (url.includes('youtube.com/watch') || url.includes('youtu.be')) {
    try {
      let videoId: string | null = null;

      if (url.includes('youtu.be')) {
        // Extract from youtu.be/VIDEO_ID format
        videoId = url.split('/').pop()?.split('?')[0] || null;
      } else if (url.includes('youtube.com/watch')) {
        // Extract from youtube.com/watch?v=VIDEO_ID format
        const urlObj = new URL(url);
        videoId = urlObj.searchParams.get('v');
      }

      if (videoId && videoId.length > 0) {
        return `https://www.youtube.com/embed/${videoId}`;
      }
    } catch (error) {
      console.warn('Error processing YouTube URL:', error);
    }
  }

  // Handle Vimeo URLs
  if (url.includes('vimeo.com')) {
    try {
      const vimeoId = url.split('/').pop()?.split('?')[0];
      if (vimeoId && !isNaN(Number(vimeoId))) {
        return `https://player.vimeo.com/video/${vimeoId}`;
      }
    } catch (error) {
      console.warn('Error processing Vimeo URL:', error);
    }
  }

  // Handle already embedded URLs
  if (url.includes('youtube.com/embed') || url.includes('player.vimeo.com/video')) {
    return url;
  }

  // Return original URL for other cases
  return url;
};
