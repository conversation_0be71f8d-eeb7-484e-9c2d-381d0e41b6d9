export interface DemographicQuestion {
  id: string;
  question: string;
  type: 'single_choice' | 'text' | 'number' | 'dropdown';
  required: boolean;
  options?: string[];
  default?: string;
  conditional?: {
    field: string;
    value: string;
  };
}

export interface DemographicQuestionnaire {
  id: string;
  title: string;
  description?: string;
  questions: DemographicQuestion[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface DemographicResponse {
  [questionId: string]: string | number;
}

export interface UserDemographicResponse {
  id: string;
  user_id: string;
  questionnaire_id: string;
  responses: DemographicResponse;
  completed_at: string;
  created_at: string;
  updated_at: string;
}

export interface DemographicAnalytics {
  total_responses: number;
  response_breakdown: {
    by_country: Record<string, number>;
    by_gender: Record<string, number>;
    by_role: Record<string, number>;
  };
  completion_rate: number;
}

export interface DemographicFormData {
  consent?: string;
  country?: string;
  gender?: string;
  age?: number;
  formal_training?: string;
  role_type?: string;
  student_level?: string;
  university?: string;
  undergraduate_program?: string;
  undergraduate_year?: string;
  postgraduate_program?: string;
  practitioner_work?: string;
  workplace?: string;
  location?: string;
  experience_years?: string;
}
