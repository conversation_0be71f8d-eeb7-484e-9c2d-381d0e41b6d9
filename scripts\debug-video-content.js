#!/usr/bin/env node

/**
 * Debug script to examine video content in lessons
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

/**
 * Get specific lesson content
 */
async function getLessonContent(lessonId) {
  const { data: lesson, error } = await supabase
    .from('lessons')
    .select('id, title, slug, content, type')
    .eq('id', lessonId)
    .single();
  
  if (error) {
    console.error(`${colors.red}❌ Error fetching lesson: ${error.message}${colors.reset}`);
    return null;
  }
  
  return lesson;
}

/**
 * Debug lesson content
 */
async function debugLessonContent(lessonId) {
  console.log(`${colors.cyan}${colors.bold}🔍 Debugging Lesson Content${colors.reset}\n`);
  
  const lesson = await getLessonContent(lessonId);
  if (!lesson) return;
  
  console.log(`${colors.blue}📄 Lesson: ${lesson.title}${colors.reset}`);
  console.log(`   ID: ${lesson.id}`);
  console.log(`   Slug: ${lesson.slug}`);
  console.log(`   Type: ${lesson.type}`);
  console.log(`   Content Length: ${lesson.content?.length || 0} characters\n`);
  
  if (!lesson.content) {
    console.log(`${colors.yellow}⚠️ No content found${colors.reset}`);
    return;
  }
  
  // Check if it's JSON
  try {
    const parsed = JSON.parse(lesson.content);
    console.log(`${colors.green}✅ Content Format: JSON (Legacy)${colors.reset}`);
    console.log(`   Keys: ${Object.keys(parsed).join(', ')}`);
    
    if (parsed.videoUrl) {
      console.log(`   Video URL: ${parsed.videoUrl}`);
    }
    
    if (parsed.content) {
      console.log(`   Text Content Length: ${parsed.content.length} characters`);
      console.log(`   Text Content Preview: ${parsed.content.substring(0, 200)}...`);
    }
  } catch {
    console.log(`${colors.green}✅ Content Format: Markdown/HTML${colors.reset}`);
    
    // Check for video content
    const hasYouTubeEmbed = lesson.content.includes('youtube.com/embed') || 
                           lesson.content.includes('data-youtube-video') ||
                           lesson.content.includes('youtube-embed');
    
    console.log(`   Has YouTube Embed: ${hasYouTubeEmbed ? colors.green + 'Yes' : colors.yellow + 'No'}${colors.reset}`);
    
    if (hasYouTubeEmbed) {
      // Extract video URLs
      const videoMatches = lesson.content.match(/youtube\.com\/embed\/([a-zA-Z0-9_-]+)/g);
      if (videoMatches) {
        console.log(`   Video URLs found: ${videoMatches.length}`);
        videoMatches.forEach((match, i) => {
          console.log(`     ${i + 1}. ${match}`);
        });
      }
      
      // Check for TipTap YouTube structure
      const tiptapYouTube = lesson.content.match(/<div[^>]*data-youtube-video[^>]*>/g);
      if (tiptapYouTube) {
        console.log(`   TipTap YouTube divs: ${tiptapYouTube.length}`);
        tiptapYouTube.forEach((match, i) => {
          console.log(`     ${i + 1}. ${match}`);
        });
      }
    }
    
    console.log(`\n${colors.cyan}📝 Content Preview:${colors.reset}`);
    console.log(lesson.content.substring(0, 500) + (lesson.content.length > 500 ? '...' : ''));
  }
}

/**
 * Main function
 */
async function main() {
  const lessonId = process.argv[2];
  
  if (!lessonId) {
    console.log(`${colors.yellow}Usage: node debug-video-content.js LESSON_ID${colors.reset}`);
    console.log(`\nKnown lesson IDs with video content:`);
    console.log(`• 1f4fa2a2-533d-4791-b59c-745742408ab6 (Handling Complications - Markdown)`);
    console.log(`• 5084cdee-0388-41d7-a812-f0283ce20d2c (Vein Selection - JSON)`);
    return;
  }
  
  await debugLessonContent(lessonId);
}

main().catch(console.error);
