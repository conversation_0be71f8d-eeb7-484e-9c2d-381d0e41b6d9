import { useEffect, useState, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import { useAuth } from '@/context/AuthContext';
import { fetchLessonBySlug, findNextLessonUnified } from '@/services/course/courseApi';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { isQuizContent } from '@/services/course/utils';
import { useToast } from '@/hooks/use-toast';
import { motion } from 'framer-motion';
import { ExternalLink, AlertCircle } from 'lucide-react';
import '@/styles/professional-lesson-content.css';
import '@/styles/lesson-content.css';
import { Button } from '@/components/ui/button';
import { LessonContentSkeleton } from '@/components/ui/loading-skeleton';
import { PageContainer } from '@/components/ui/floating-sidebar-container';

// Imported components
import LessonContent from '@/components/course/LessonContent';
import LessonFooter from '@/components/course/LessonFooter';
import UnifiedQuizContent from '@/components/course/UnifiedQuizContent';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Add new import
import { SchemaService } from '@/services/db-schema';
import { useLessonCompletion } from '@/hooks/use-lesson-completion';

const LessonPage = () => {
  const { courseId = '', lessonId = '' } = useParams();
  const { user } = useAuth();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [progress, setProgress] = useState(0);
  const [isNavigating, setIsNavigating] = useState(false);
  const [hasExternalRedirect, setHasExternalRedirect] = useState<string | null>(null);
  const [schemaErrorOpen, setSchemaErrorOpen] = useState(false);
  const [isRepairingSchema, setIsRepairingSchema] = useState(false);

  // Keep track of the last progress update time to avoid excessive updates
  const lastProgressRef = useRef(0);
  const scrollTimeoutRef = useRef<number | null>(null);
  const isMountedRef = useRef(true);

  // Fetch the lesson data
  const { data: lesson, isLoading, error } = useQuery({
    queryKey: ['lesson', lessonId],
    queryFn: async () => {
      console.log('[LESSON PAGE] Fetching lesson with slug:', lessonId, 'userId:', user?.id);
      try {
        const result = await fetchLessonBySlug(lessonId, user?.id);
        console.log('[LESSON PAGE] Successfully fetched lesson:', result);
        return result;
      } catch (err) {
        console.error('[LESSON PAGE] Error fetching lesson:', err);
        throw err;
      }
    },
    enabled: !!lessonId,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Only use the completion hook when we have a valid lesson ID
  const { completeMutation } = useLessonCompletion({
    lessonId: lesson?.id || '', // Use the actual lesson ID from fetched data, not the slug
    lessonSlug: lessonId, // Pass the slug for error reporting
    userId: user?.id || '',
    courseId,
    onSchemaError: () => setSchemaErrorOpen(true),
    // Only enable the mutation when we have an actual lesson with ID loaded
    enabled: !!lesson?.id
  });

  // Check for external redirect URL in lesson content
  useEffect(() => {
    if (lesson?.content) {
      try {
        const contentObj = JSON.parse(lesson.content);
        if (contentObj && contentObj.externalRedirectUrl) {
          setHasExternalRedirect(contentObj.externalRedirectUrl);
        }
      } catch (e) {
        // Not JSON content or doesn't have externalRedirectUrl
        setHasExternalRedirect(null);
      }
    }
  }, [lesson]);

  // Create a memoized navigation function
  const navigateToNextLesson = useCallback(async () => {
    console.log('[NAVIGATE TO NEXT] Starting navigation from lesson:', lessonId);

    if (isNavigating) {
      console.log('[NAVIGATE TO NEXT] Already navigating, skipping');
      return;
    }
    setIsNavigating(true);

    try {
      console.log('[NAVIGATE TO NEXT] Calling findNextLessonUnified...');
      const { nextLessonSlug, isLastLesson } = await findNextLessonUnified(lessonId);

      console.log('[NAVIGATE TO NEXT] Result from findNextLessonUnified:', {
        nextLessonSlug,
        isLastLesson,
        currentLessonId: lessonId,
        courseId
      });

      if (nextLessonSlug) {
        // If there's a next lesson, navigate to it
        console.log('[NAVIGATE TO NEXT] Navigating to next lesson:', nextLessonSlug);
        navigate(`/course/${courseId}/lesson/${nextLessonSlug}`);
      } else if (isLastLesson) {
        // If this is the last lesson, navigate back to the modules page
        console.log('[NAVIGATE TO NEXT] This is the last lesson, going to modules page');
        navigate(`/course/${courseId}/modules`);
        toast({
          title: "Congratulations!",
          description: "You've completed all lessons in this course!",
        });
      } else {
        // Something went wrong, just go back to the modules page
        console.log('[NAVIGATE TO NEXT] No next lesson found and not marked as last lesson, going to modules page');
        navigate(`/course/${courseId}/modules`);
      }
    } catch (error) {
      console.error('[NAVIGATE TO NEXT] Error navigating to next lesson:', error);
      navigate(`/course/${courseId}/modules`);
    } finally {
      setIsNavigating(false);
    }
  }, [lessonId, courseId, navigate, toast, isNavigating]);

  // Set mounted flag on component mount and cleanup on unmount
  useEffect(() => {
    isMountedRef.current = true;

    return () => {
      isMountedRef.current = false;

      // Clean up any pending timeouts
      if (scrollTimeoutRef.current) {
        window.clearTimeout(scrollTimeoutRef.current);
        scrollTimeoutRef.current = null;
      }
    };
  }, []);

  // Update the handleRepairSchema function to use our improved SchemaService
  const handleRepairSchema = async () => {
    setIsRepairingSchema(true);
    try {
      // First try the comprehensive repair function that handles everything
      const success = await SchemaService.completeSystemRepair();

      if (!success) {
        console.log('Complete system repair failed, trying individual repairs...');

        // Try repairing the schema structure
        const schemaSuccess = await SchemaService.repairLessonProgressSchema();
        if (!schemaSuccess) {
          console.log('Schema repair failed, trying RLS policy fixes...');
        }

        // Try fixing RLS policies regardless of schema repair success
        const rlsSuccess = await SchemaService.fixRlsPolicies();
        if (!rlsSuccess) {
          console.log('RLS policy fixes failed, trying module tracking fixes...');
        }

        // Try fixing module tracking RLS specifically
        const moduleRlsSuccess = await SchemaService.fixModuleTrackingRLS();
        if (!moduleRlsSuccess) {
          console.log('Module RLS fixes failed, trying performance optimizations...');
        }

        // Try adding performance indexes
        const indexSuccess = await SchemaService.addPerformanceIndexes();
        if (!indexSuccess) {
          console.log('Performance index creation failed, trying direct module update...');
        }

        // Try direct module progress update if lesson is loaded
        if (lesson && user) {
          const directSuccess = await SchemaService.updateModuleProgressDirectly(
            user.id,
            lesson.module_id,
            false // Not completed yet
          );

          if (!directSuccess) {
            throw new Error('All repair attempts failed');
          }
        } else {
          throw new Error('Could not repair without lesson or user data');
        }
      }

      // Invalidate queries to refresh data if component is still mounted
      if (isMountedRef.current) {
        queryClient.invalidateQueries({ queryKey: ['lesson', lessonId] });

        toast({
          title: "Repair Complete",
          description: "Database access issues have been fixed. Please try again.",
        });
      }
    } catch (error: any) {
      console.error('Error repairing database access:', error);

      if (isMountedRef.current) {
        toast({
          title: "Error",
          description: "Could not repair database access. You can continue using the app, but progress may only be saved locally.",
          variant: "destructive",
        });
      }
    } finally {
      if (isMountedRef.current) {
        setIsRepairingSchema(false);
        setSchemaErrorOpen(false);
      }
    }
  };

  // Optimized scroll tracking with debouncing and requestAnimationFrame
  useEffect(() => {
    if (!lesson || lesson.type === 'quiz' || lesson.completed) return;

    // Don't track progress for external redirects
    if (hasExternalRedirect) {
      // Auto-complete after a short delay
      const timer = setTimeout(() => {
        setProgress(100);
        if (!lesson.completed) {
          completeMutation.mutate();
        }
      }, 2000);

      return () => clearTimeout(timer);
    }

    // Check if the content is rich (has video/image)
    let hasRichMedia = false;
    if (lesson.content && lesson.content.startsWith('{')) {
      try {
        const contentObj = JSON.parse(lesson.content);
        hasRichMedia = !!(contentObj.videoUrl || contentObj.imageUrl);
      } catch (e) {
        // Not rich content
      }
    }

    // For lessons with videos, we'll track progress differently (not with scroll)
    if (hasRichMedia) {
      // Auto-complete after some time viewing the media
      const timer = setTimeout(() => {
        setProgress(100);
        if (!lesson.completed) {
          completeMutation.mutate();
        }
      }, 30000); // 30 seconds

      return () => clearTimeout(timer);
    } else {
      // Highly optimized scroll tracking with debounce and rAF
      let scrollTicking = false;
      let lastScrollY = 0;
      let lastUpdateTime = 0;

      const handleScroll = () => {
        lastScrollY = window.scrollY;

        // Skip if we're still processing a frame or not enough time has passed
        if (scrollTicking) return;

        // Use requestAnimationFrame for better performance
        scrollTicking = true;
        window.requestAnimationFrame(() => {
          // Rate limit updates to max once per 300ms
          const now = Date.now();
          if (now - lastUpdateTime < 300) {
            scrollTicking = false;
            return;
          }

          // Calculate scroll progress with error handling
          const scrollHeight = document.documentElement.scrollHeight - window.innerHeight;
          if (scrollHeight <= 0) {
            scrollTicking = false;
            return;
          }

          // Update progress smoothly
          const rawProgress = Math.min(Math.floor((lastScrollY / scrollHeight) * 100), 100);

          // Only update state if progress changed significantly (more than 5%)
          if (Math.abs(rawProgress - lastProgressRef.current) > 5) {
            lastProgressRef.current = rawProgress;
            lastUpdateTime = now;
            setProgress(rawProgress);

            // Auto-complete when user reaches the bottom (90% of content)
            if (rawProgress > 90 && !lesson.completed && !scrollTimeoutRef.current) {
              scrollTimeoutRef.current = window.setTimeout(() => {
                completeMutation.mutate();
                scrollTimeoutRef.current = null;
              }, 2000); // Longer delay to avoid completing too quickly
            }
          }

          scrollTicking = false;
        });
      };

      // Use passive event listener for better performance
      window.addEventListener('scroll', handleScroll, { passive: true });

      // Initial calculation on mount
      setTimeout(() => handleScroll(), 500);

      // Clean up event listeners and timeouts
      return () => {
        window.removeEventListener('scroll', handleScroll);
        if (scrollTimeoutRef.current) {
          window.clearTimeout(scrollTimeoutRef.current);
          scrollTimeoutRef.current = null;
        }
      };
    }
  }, [lesson, completeMutation, hasExternalRedirect]);

  // Handle manual completion
  const handleCompleteLesson = useCallback(async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to mark a lesson as completed.",
        variant: "destructive",
      });
      return;
    }

    if (!lesson) {
      toast({
        title: "Error",
        description: "Lesson data not found.",
        variant: "destructive",
      });
      return;
    }

    // Don't try to complete if already completed
    if (lesson.completed) {
      console.log("Lesson already completed, no need to mark again");
      return;
    }

    try {
      console.log("Attempting to mark lesson as completed...", {
        lessonId: lesson.id,
        slug: lessonId
      });
      await completeMutation.mutateAsync();

      // Update local state right away
      if (lesson) {
        lesson.completed = true;
      }

      console.log("Lesson completion mutation succeeded");
    } catch (error: any) {
      console.error('Error in handleCompleteLesson:', error);
      // Error handling is done in the mutation
    }
  }, [user, lesson, completeMutation, toast, lessonId]);

  // External link handler
  const handleOpenExternalForm = useCallback(() => {
    if (hasExternalRedirect) {
      window.open(hasExternalRedirect, '_blank');
    }
  }, [hasExternalRedirect]);

  // Loading state
  if (isLoading) {
    return (
      <Layout>
        <PageContainer pageType="default">
          <LessonContentSkeleton />
        </PageContainer>
      </Layout>
    );
  }

  // Error state
  if (error || !lesson) {
    return (
      <Layout>
        <PageContainer pageType="default">
          <div className="flex flex-col items-center justify-center p-8 bg-destructive/10 rounded-lg">
            <h1 className="text-xl font-bold mb-4">Error loading lesson</h1>
            <p className="text-muted-foreground text-center mb-6">
              {(error as any)?.message || 'Could not load lesson content. Please try again later.'}
            </p>
            <div className="flex gap-4">
              <Button variant="outline" onClick={() => navigate(`/course/${courseId}/modules`)}>
                Back to Modules
              </Button>
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
            </div>
          </div>
        </PageContainer>
      </Layout>
    );
  }

  const contentType = isQuizContent(lesson.content) ? 'quiz' : 'content';

    return (
      <Layout>
        <PageContainer pageType="default">
        {contentType === 'quiz' ? (
          <UnifiedQuizContent
            content={lesson.content}
            onComplete={navigateToNextLesson}
            lessonId={lesson.id}
          />
        ) : (
          <>


            {/* Lesson content with consistent spacing */}
            <LessonContent content={lesson.content} />

            {/* Footer with consistent spacing */}
            <div className="mt-8">
              <LessonFooter
                isCompleted={lesson.completed}
                onComplete={handleCompleteLesson}
                onNext={navigateToNextLesson}
                completionMutation={completeMutation}
                hasNextLesson={!!lesson.next_lesson_slug}
                progress={progress}
                courseId={courseId}
                currentLessonSlug={lessonId}
              />

              {/* External redirect button for Google Form */}
              {hasExternalRedirect && (
                <motion.div
                  className="mt-6 mb-4 flex flex-col items-center justify-center p-6 bg-primary/5 rounded-xl border border-primary/20"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <h2 className="text-xl font-semibold mb-4">External Form</h2>
                  <p className="text-center mb-6">
                    You are being redirected to an external form. If you are not redirected automatically, please click the button below.
                  </p>
                  <Button
                    size="lg"
                    onClick={handleOpenExternalForm}
                    className="flex items-center"
                  >
                    <ExternalLink className="mr-2 h-5 w-5" />
                    Open External Form
                  </Button>
                </motion.div>
              )}
            </div>
          </>
        )}
        </PageContainer>

      {/* Schema Repair Dialog */}
      <AlertDialog open={schemaErrorOpen} onOpenChange={setSchemaErrorOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Database Access Issue Detected</AlertDialogTitle>
            <AlertDialogDescription>
              There appears to be an issue with the progress tracking system. This can happen due to database schema changes, caching issues, or permission restrictions.
              <div className="mt-4 p-4 bg-muted rounded-md">
                <div className="flex items-start">
                  <AlertCircle className="h-5 w-5 text-amber-500 mr-2 mt-0.5" />
                  <p className="text-sm">
                    Error: {completeMutation.error?.message ||
                      "Could not update your progress. This might be due to database permission restrictions."
                    }
                  </p>
                </div>
              </div>
              <p className="mt-4">The system will continue to function but your progress may only be saved locally. Would you like to attempt to repair this issue now?</p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Continue Without Fixing</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRepairSchema}
              disabled={isRepairingSchema}
            >
              {isRepairingSchema ? "Repairing..." : "Repair Database Access"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Layout>
  );
};

export default LessonPage;
