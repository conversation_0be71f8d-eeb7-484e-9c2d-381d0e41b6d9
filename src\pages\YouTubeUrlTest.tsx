import React, { useState } from 'react';
import Layout from '../components/Layout';
import { PageContainer } from '@/components/ui/floating-sidebar-container';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

const YouTubeUrlTest = () => {
  const [testUrl, setTestUrl] = useState('https://www.youtube.com/watch?v=dQw4w9WgXcQ');
  const [results, setResults] = useState<any[]>([]);

  // Convert YouTube URL to embed format
  const convertToEmbedUrl = (url: string): string => {
    if (!url) return '';
    
    // If already an embed URL, return as is
    if (url.includes('youtube.com/embed/')) {
      return url;
    }
    
    // Extract video ID from various YouTube URL formats
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)/,
      /youtube\.com\/embed\/([^&\n?#]+)/
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match && match[1]) {
        return `https://www.youtube.com/embed/${match[1]}`;
      }
    }
    
    return url; // Return original if no match
  };

  const testUrlConversion = () => {
    const testUrls = [
      'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      'https://youtu.be/dQw4w9WgXcQ',
      'https://www.youtube.com/embed/dQw4w9WgXcQ',
      'https://youtube.com/watch?v=dQw4w9WgXcQ',
      testUrl
    ];

    const testResults = testUrls.map(url => {
      const converted = convertToEmbedUrl(url);
      const isValid = converted.includes('youtube.com/embed/');
      const videoId = converted.match(/embed\/([^?&]+)/)?.[1] || 'Not found';
      
      return {
        original: url,
        converted,
        isValid,
        videoId,
        status: isValid ? 'success' : 'error'
      };
    });

    setResults(testResults);
  };

  return (
    <Layout>
      <PageContainer pageType="default">
        <div className="max-w-4xl mx-auto space-y-6">
          <div>
            <h1 className="text-2xl font-bold mb-2">YouTube URL Conversion Test</h1>
            <p className="text-muted-foreground">
              Test YouTube URL conversion to ensure proper embed format.
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>URL Conversion Test</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={testUrl}
                  onChange={(e) => setTestUrl(e.target.value)}
                  placeholder="Enter YouTube URL to test..."
                  className="flex-1"
                />
                <Button onClick={testUrlConversion}>Test Conversion</Button>
              </div>

              {results.length > 0 && (
                <div className="space-y-3">
                  <h3 className="font-semibold">Test Results:</h3>
                  {results.map((result, index) => (
                    <div key={index} className="border rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Test {index + 1}</span>
                        <Badge variant={result.status === 'success' ? 'default' : 'destructive'}>
                          {result.status}
                        </Badge>
                      </div>
                      
                      <div className="space-y-1 text-xs">
                        <div><strong>Original:</strong> {result.original}</div>
                        <div><strong>Converted:</strong> {result.converted}</div>
                        <div><strong>Video ID:</strong> {result.videoId}</div>
                        <div><strong>Valid Embed URL:</strong> {result.isValid ? '✅' : '❌'}</div>
                      </div>

                      {result.isValid && (
                        <div className="mt-3">
                          <div className="text-xs font-medium mb-1">Preview:</div>
                          <div className="bg-gray-100 p-2 rounded">
                            <iframe
                              src={result.converted}
                              width="300"
                              height="169"
                              frameBorder="0"
                              allowFullScreen
                              title={`YouTube video ${result.videoId}`}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Fix Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold mb-2">✅ Issues Fixed:</h3>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li><strong>URL Conversion</strong>: Added proper YouTube watch → embed URL conversion</li>
                  <li><strong>Embed Format</strong>: Ensured all YouTube URLs use `https://www.youtube.com/embed/VIDEO_ID` format</li>
                  <li><strong>TipTap Integration</strong>: Updated SimpleMarkdownEditor to convert URLs before passing to TipTap</li>
                  <li><strong>Serialization</strong>: Enhanced markdown serializer always outputs proper embed URLs</li>
                </ul>
              </div>
              
              <div>
                <h3 className="font-semibold mb-2">🔧 Technical Solution:</h3>
                <ol className="list-decimal list-inside space-y-1 text-sm">
                  <li>User pastes any YouTube URL format (watch, youtu.be, embed)</li>
                  <li>URL gets converted to proper embed format before TipTap insertion</li>
                  <li>TipTap creates youtube node with correct embed URL</li>
                  <li>Serializer outputs iframe with proper embed URL</li>
                  <li>Video displays correctly without "refused to connect" errors</li>
                </ol>
              </div>

              <div>
                <h3 className="font-semibold mb-2">📋 Test Cases:</h3>
                <ul className="list-disc list-inside space-y-1 text-xs">
                  <li>https://www.youtube.com/watch?v=VIDEO_ID → https://www.youtube.com/embed/VIDEO_ID</li>
                  <li>https://youtu.be/VIDEO_ID → https://www.youtube.com/embed/VIDEO_ID</li>
                  <li>https://www.youtube.com/embed/VIDEO_ID → No change (already correct)</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </PageContainer>
    </Layout>
  );
};

export default YouTubeUrlTest;
