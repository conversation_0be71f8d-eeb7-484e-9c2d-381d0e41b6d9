import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion, useAnimation, useScroll, useTransform } from 'framer-motion';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  BookOpen,
  Clock,
  Award,
  ArrowRight,
  CheckCircle2,
  Stethoscope,
  GraduationCap,
  Users,
  School,
  FileText,
  Mail,
  Phone,
  MapPin,
  Building,
  ChevronRight,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import MainNavigation from '@/components/MainNavigation';
import { ThemeToggle } from '@/components/theme/theme-toggle';

// Hero carousel images
const heroImages = [
  "/images/hero1.jpg",
  "/images/hero2.jpg",
  "/images/hero3.jpg",
  "/images/hero4.jpg",
  "/images/hero5.jpg",
  "/images/hero6.jpg"
];

const teamMembers = [
  {
    name: "<PERSON> <PERSON>",
    role: "Department of Medical Imaging",
    institution: "KNUST, Ghana"
  },
  {
    name: "Prof <PERSON><PERSON>",
    role: "Department of Medical Imaging",
    institution: "KNUST, Ghana"
  },
  {
    name: "Mr <PERSON>",
    role: "Department of Medical Imaging",
    institution: "KNUST, Ghana"
  },
  {
    name: "Miss Maame Araba Ekubi Acquaye",
    role: "Department of Medical Imaging",
    institution: "KNUST, Ghana"
  },
  {
    name: "Mr Opoku Sekyere",
    role: "Department of Medical Imaging",
    institution: "KNUST, Ghana"
  },
  {
    name: "Mr Alexander Dzakah Jnr",
    role: "Department of Medical Imaging",
    institution: "KNUST, Ghana"
  },
  {
    name: "Mr Kwabena Asamoah Baffour",
    role: "Department of Medical Imaging",
    institution: "KNUST, Ghana"
  },
  {
    name: "Miss Adwoa Agyemang Duah",
    role: "Department of Medical Imaging",
    institution: "KNUST, Ghana"
  },
  {
    name: "Mr Prince Akrofi Osakonor",
    role: "Department of Medical Imaging",
    institution: "KNUST, Ghana"
  }
];

// Features list
const features = [
  {
    icon: <BookOpen className="h-8 w-8 text-primary" />,
    title: "Comprehensive Learning",
    description: "Access evidence-based modules designed specifically for medical imaging professionals"
  },
  {
    icon: <Clock className="h-8 w-8 text-primary" />,
    title: "Learn at Your Pace",
    description: "Self-paced modules allow you to learn whenever and wherever is convenient for you"
  },
  {
    icon: <Award className="h-8 w-8 text-primary" />,
    title: "Earn Certifications",
    description: "Receive recognition for your completed modules with downloadable certificates"
  }
];

const Index = () => {
  const isMobile = useIsMobile();
  const { scrollY } = useScroll();
  const backgroundColor = useTransform(
    scrollY, 
    [0, 1000], 
    ["#fefefe", "#fefefe"]  // Subtle white background
  );
  
  // State for the hero image carousel
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  
  // Auto-advance the carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % heroImages.length);
    }, 5000); // Change image every 5 seconds
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <div className="min-h-screen relative" style={{ background: backgroundColor as unknown as string }}>
      {/* Content Container */}
      <div className="relative z-10">
        {/* Sticky Header with Mobile Optimization */}
        <header className="fixed top-0 left-0 right-0 bg-background/95 dark:bg-background/95 border-b border-border/40 z-50 supports-[backdrop-filter]:bg-background/60 supports-[backdrop-filter]:backdrop-blur-sm">
          <div className="container mx-auto px-4 sm:px-6">
            <div className="flex h-14 sm:h-16 items-center justify-between">
              <motion.h1
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-lg sm:text-xl font-extrabold text-primary flex items-center"
              >
                <GraduationCap className="h-6 w-6 sm:h-8 sm:w-8 sm:mr-2" />
                <span>e4mi</span>
              </motion.h1>

              {/* Mobile-optimized Navigation */}
              <div className="flex items-center gap-2 sm:gap-4">
                <MainNavigation />
                <div className="flex items-center gap-1.5 sm:gap-2">
                  <ThemeToggle />
                  <Button 
                    asChild
                    variant="default"
                    size={isMobile ? "sm" : "default"}
                    className="shadow-sm font-medium"
                  >
                    <Link to="/login">
                      Login
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main>
          {/* Hero Section */}
          <section className="pt-32 pb-16 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
            <div className="max-w-7xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                <div className="space-y-8">
                  <div>
              <motion.div
                      initial={{ opacity: 0, y: -20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6 }}
                      className="mb-2"
                    >
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary/10 text-primary">
                        Medical Imaging Education
                      </span>
                    </motion.div>
                    <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.1 }}
                      className="text-4xl sm:text-5xl font-bold text-foreground leading-tight mb-4"
                    >
                      Welcome to the <span className="text-primary">e4mi</span> platform
                    </motion.h1>
                    <motion.p
                      initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.6, delay: 0.2 }}
                      className="text-xl text-muted-foreground"
                    >
                      Providing an evidence-based eLearning platform to train the medical imaging workforce
                    </motion.p>
                  </div>
                
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.3 }}
                    className="flex flex-wrap gap-4"
                  >
                <Button 
                  asChild
                  size="lg"
                      className="px-8 py-6 h-auto text-base font-medium relative"
                >
                  <Link to="/login" state={{ showSignup: true }}>
                    Get Started
                        <ChevronRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button 
                  asChild
                  variant="outline" 
                  size="lg"
                      className="px-8 py-6 h-auto text-base font-medium"
                >
                  <Link to="/login">
                    Login
                  </Link>
                </Button>
                  </motion.div>
                </div>
                
                {/* Hero Image Carousel */}
                <motion.div
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                  className="relative h-[300px] sm:h-[400px] rounded-xl overflow-hidden shadow-lg"
                >
                  {heroImages.map((img, index) => (
                    <motion.div
                      key={index}
                      className="absolute inset-0"
                      initial={{ opacity: 0 }}
                      animate={{ 
                        opacity: index === currentImageIndex ? 1 : 0
                      }}
                      transition={{ 
                        opacity: { duration: 1 }
                      }}
                    >
                      <img
                        src={img}
                        alt={`Medical imaging training ${index + 1}`}
                        className="w-full h-full object-cover rounded-xl"
                      />
                    </motion.div>
                  ))}
                  
                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-transparent rounded-xl" />
                </motion.div>
              </div>
            </div>
          </section>
          
          {/* Features Section */}
          <section className="py-16 sm:py-24 px-4 sm:px-6 lg:px-8 bg-muted/30">
            <div className="max-w-7xl mx-auto">
              <div className="text-center mb-12">
                <motion.h2
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.6 }}
                  className="text-3xl font-bold text-foreground mb-4"
                >
                  Why Choose Our Platform?
                </motion.h2>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="w-24 h-1 bg-primary mx-auto mb-6"
                ></motion.div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true, margin: "-100px" }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="bg-card shadow-sm hover:shadow-md transition-shadow duration-300 rounded-xl p-8 text-center"
                  >
                    <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-primary/10 mb-6">
                      {feature.icon}
                    </div>
                    <h3 className="text-xl font-semibold text-foreground mb-3">{feature.title}</h3>
                    <p className="text-muted-foreground">{feature.description}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>

          {/* About the Programme */}
          <section className="py-16 sm:py-24 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.7 }}
                >
                  <div className="bg-muted/20 p-1 rounded-2xl">
                    <div className="relative h-[350px] overflow-hidden rounded-xl">
                      <img 
                        src="/images/hero3.jpg" 
                        alt="Medical imaging training" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                </div>
                </motion.div>
                
                <motion.div 
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.7 }}
                  className="space-y-6"
                >
                  <div>
                    <h2 className="text-3xl font-bold text-foreground mb-2">
                      About Our Programmes
                    </h2>
                    <div className="w-20 h-1 bg-primary mb-6"></div>
                  </div>
                  
                  <h3 className="text-xl font-semibold text-foreground">
                  Intravenous Cannulation and Contrast Administration (I-can-IV eLearning) initiative
                  </h3>
                  
                  <p className="text-muted-foreground">
                  One of our ongoing programmes is the I-can-IV eLearning initiative. The I-can-IV eLearning initiative was developed by the Department of Medical Imaging, KNUST. The intravenous cannulation and contrast administration (I-can-IV eLearning) programme was developed by the Department of Medical Imaging, KNUST.
                  </p>
                  <p className="text-muted-foreground">
                    Intravenous (IV) cannulation and contrast administration are essential clinical skills required in various medical imaging settings. The I-can-IV eLearning initiative provides medical imaging students and professionals with knowledge and guidance on how to safely and effectively perform IV cannulation.
                  </p>
                  <p className="text-muted-foreground">
                    The I-can-IV eLearning initiative covers important topics such as overview of IV cannulation, patient preparation and procedure for IV cannulation, contrast administration, post administrative care and IV removal and infection control practices during IV cannulation. Medical imaging students and professionals gaining knowledge of IV cannulation and contrast administration will enhance patient care, reduce the risk of complications and build confidence in clinical practices.
                  </p>
                  
                  <Button asChild className="mt-4">
                    <Link to="/login">
                      Start Learning Now
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </motion.div>
              </div>
            </div>
          </section>

          {/* How to Access */}
          <section className="py-16 sm:py-24 px-4 sm:px-6 lg:px-8 bg-muted/30">
            <div className="max-w-7xl mx-auto text-center">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-100px" }}
                transition={{ duration: 0.6 }}
                className="max-w-3xl mx-auto"
              >
                <h2 className="text-3xl font-bold text-foreground mb-4">
                  How to Access
                  </h2>
                <div className="w-24 h-1 bg-primary mx-auto mb-6"></div>
                <p className="text-lg text-muted-foreground mb-8">
                    To access the I-can-IV eLearning programme, you will need an account. You can register by selecting the Get Started button. If you already have an account, click the Login button.
                  </p>
                  
                <div className="flex flex-wrap justify-center gap-4 mt-8">
                    <Button 
                      asChild
                      size="lg"
                    className="shadow-md font-medium px-8 py-6 h-auto text-base"
                    >
                      <Link to="/login" state={{ showSignup: true }}>
                         Get Started
                        <ChevronRight className="ml-2 h-4 w-4" />
                  </Link>
                    </Button>
                    <Button 
                      asChild
                      variant="outline" 
                      size="lg"
                    className="shadow-sm font-medium px-8 py-6 h-auto text-base"
                    >
                      <Link to="/login">
                        Login
                      </Link>
                    </Button>
                  </div>
              </motion.div>
            </div>
          </section>

          {/* The Team */}
          <section className="py-16 sm:py-24 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto">
              <div className="text-center mb-12">
                <motion.h2
                initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.6 }}
                  className="text-3xl font-bold text-foreground mb-4"
                >
                  Meet Our Team
                </motion.h2>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="w-24 h-1 bg-primary mx-auto mb-6"
                ></motion.div>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                    {teamMembers.map((member, index) => (
                  <motion.div
                        key={index}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true, margin: "-100px" }}
                    transition={{ duration: 0.5, delay: index * 0.05 }}
                    className="bg-card shadow-sm hover:shadow-md transition-shadow duration-300 rounded-xl p-6"
                  >
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0 w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                        <Users className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-foreground">{member.name}</h3>
                        <p className="text-muted-foreground text-sm">{member.role}</p>
                        <p className="text-muted-foreground text-sm">{member.institution}</p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>

          {/* Contact Us */}
          <section className="py-16 sm:py-24 px-4 sm:px-6 lg:px-8 bg-muted/30">
            <div className="max-w-7xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.7 }}
                  className="space-y-6"
                >
                  <div>
                    <h2 className="text-3xl font-bold text-foreground mb-2">
                      Contact Us
                    </h2>
                    <div className="w-20 h-1 bg-primary mb-6"></div>
                  </div>
                  
                  <p className="text-lg text-muted-foreground">
                    Have questions about the e4mi platform? Our team is here to help you.
                  </p>
                  
                  <div className="space-y-4 mt-6">
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                      <Mail className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="text-muted-foreground">Email</p>
                        <p className="text-foreground font-medium"><EMAIL></p>
                      </div>
                  </div>
                  
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <Phone className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="text-muted-foreground">Phone</p>
                        <p className="text-foreground font-medium">+233 (0) 559 917 082</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <Building className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <p className="text-muted-foreground">Department</p>
                        <p className="text-foreground font-medium">Department of Medical Imaging</p>
                        <p className="text-muted-foreground">Kwame Nkrumah University of Science and Technology (KNUST), Ghana</p>
                      </div>
                    </div>
                  </div>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, x: 30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true, margin: "-100px" }}
                  transition={{ duration: 0.7 }}
                  className="bg-card shadow-md rounded-xl overflow-hidden"
                >
                  <div className="aspect-video w-full">
                    <img 
                      src="/images/hero6.jpg" 
                      alt="KNUST Department of Medical Imaging" 
                      className="w-full h-full object-cover"
                    />
                  </div>
              </motion.div>
              </div>
            </div>
          </section>
        </main>

        {/* Footer */}
        <footer className="bg-card/80 border-t border-border/40 py-12">
          <div className="container mx-auto px-4 sm:px-6">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="mb-6 md:mb-0">
                <div className="flex items-center gap-2 mb-3">
                  <GraduationCap className="h-6 w-6 text-primary" />
                  <span className="text-xl font-bold">e4mi</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  eLearning for Medical Imaging Workforce
                </p>
              </div>
              
              <div className="mb-6 md:mb-0 text-center md:text-left">
                <p className="text-sm text-muted-foreground">
                  Department of Medical Imaging<br />
                  Kwame Nkrumah University of Science and Technology
                </p>
              </div>
              
              <div className="text-center md:text-right">
              <p className="text-sm text-muted-foreground">
                © {new Date().getFullYear()} KNUST. All rights reserved.
              </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default Index;
