import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { CheckCircle, ArrowRight, Loader2 } from 'lucide-react';
import { UseMutationResult } from '@tanstack/react-query';
import { findNextLessonUnified } from '@/services/course/courseApi';
import { useAuth } from '@/context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

export interface LessonFooterProps {
  isCompleted: boolean;
  onComplete: () => void;
  onNext: () => void;
  completionMutation: UseMutationResult<any, any, any>;
  hasNextLesson: boolean;
  progress: number;
  courseId: string;
  currentLessonSlug: string;
}

const LessonFooter: React.FC<LessonFooterProps> = ({
  isCompleted,
  onComplete,
  onNext,
  completionMutation,
  hasNextLesson,
  progress,
  courseId,
  currentLessonSlug
}) => {
  const [buttonClicked, setButtonClicked] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const { user } = useAuth();
  const navigate = useNavigate();

  // Handle completion button click with debounce
  const handleCompleteClick = () => {
    if (buttonClicked || completionMutation.isPending) return;

    // Set local state to prevent double clicks
    setButtonClicked(true);

    // Call the actual completion handler
    onComplete();

    // Reset after a delay (just in case the mutation fails)
    setTimeout(() => setButtonClicked(false), 2000);
  };

  // Handle next item click - complete lesson first, then navigate to next item (lesson or test)
  const handleNextClick = async () => {
    if (isNavigating || completionMutation.isPending || !user) return;

    setIsNavigating(true);

    try {
      console.log('[LESSON FOOTER] Starting next lesson navigation from:', currentLessonSlug);

      // If lesson is not completed, mark it as completed first
      if (!isCompleted) {
        console.log('[LESSON FOOTER] Completing lesson before navigation');
        await completionMutation.mutateAsync();
      }

      // Find the next lesson using unified navigation
      console.log('[LESSON FOOTER] Finding next lesson using unified navigation');
      const { nextLessonSlug, isLastLesson } = await findNextLessonUnified(currentLessonSlug);

      console.log('[LESSON FOOTER] Navigation result:', { nextLessonSlug, isLastLesson });

      if (nextLessonSlug) {
        // Navigate to next lesson
        console.log('[LESSON FOOTER] Navigating to next lesson:', nextLessonSlug);
        navigate(`/course/${courseId}/lesson/${nextLessonSlug}`);
      } else if (isLastLesson) {
        // This is the last lesson, go to modules page
        console.log('[LESSON FOOTER] Last lesson reached, going to modules page');
        navigate(`/course/${courseId}/modules`);
      } else {
        // Fallback to original onNext behavior
        console.log('[LESSON FOOTER] Using fallback onNext behavior');
        onNext();
      }
    } catch (error) {
      console.error('[LESSON FOOTER] Error during navigation:', error);
      console.error('[LESSON FOOTER] Error details:', {
        currentLessonSlug,
        courseId,
        isCompleted,
        error: error instanceof Error ? error.message : String(error)
      });

      // Fallback navigation - go to modules page to be safe
      console.log('[LESSON FOOTER] Using emergency fallback - going to modules page');
      navigate(`/course/${courseId}/modules`);
    } finally {
      setIsNavigating(false);
    }
  };

  // Track if button is disabled
  const isButtonDisabled = buttonClicked || completionMutation.isPending;

  return (
    <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-6 border-t">
      {/* Completion section */}
      <div className="flex items-center gap-2">
        {isCompleted ? (
          <div className="flex items-center gap-2 text-green-600 bg-green-50 px-4 py-2 rounded-md dark:bg-green-900/20 dark:text-green-400">
            <CheckCircle className="h-5 w-5" />
            <span>Completed</span>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <div className="w-24 bg-muted rounded-full h-2 overflow-hidden">
              <motion.div
                className="h-full bg-primary"
                style={{ width: `${progress}%` }}
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
            <span className="text-sm text-muted-foreground">{progress}%</span>

            <Button
              onClick={handleCompleteClick}
              disabled={isButtonDisabled}
              variant="outline"
              className="ml-2 min-w-[140px]"
            >
              {isButtonDisabled ? (
                <span className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Marking...
                </span>
              ) : (
                'Mark as Complete'
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Next lesson button */}
      <Button
        onClick={handleNextClick}
        disabled={isNavigating || completionMutation.isPending}
        variant="default"
        size="lg"
        className="flex items-center gap-2"
      >
        {isNavigating ? (
          <>
            <Loader2 className="h-4 w-4 animate-spin" />
            {isCompleted ? 'Loading...' : 'Completing...'}
          </>
        ) : (
          <>
            Next
            <ArrowRight className="h-4 w-4" />
          </>
        )}
      </Button>
    </div>
  );
};

export default LessonFooter;
